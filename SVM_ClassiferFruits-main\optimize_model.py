"""
优化SVM模型参数，提升分类性能
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import GridSearchCV, StratifiedShuffleSplit
from sklearn.svm import SVC
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler
import Data_Classifer as svm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def prepare_data():
    """准备和预处理数据"""
    print("准备数据...")
    
    # 加载数据
    data = svm.data1_file()
    features = []
    labels = []
    
    for feature, label in data:
        features.append(feature)
        labels.append(label)
    
    features = np.array(features)
    labels = np.array(labels)
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # 分层抽样分割数据
    sss = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
    train_idx, test_idx = next(sss.split(features_scaled, labels))
    
    X_train = features_scaled[train_idx]
    X_test = features_scaled[test_idx]
    y_train = labels[train_idx]
    y_test = labels[test_idx]
    
    print(f"训练集大小: {len(X_train)}")
    print(f"测试集大小: {len(X_test)}")
    
    return X_train, X_test, y_train, y_test, scaler

def optimize_svm_parameters(X_train, y_train):
    """优化SVM参数"""
    print("\n开始参数优化...")
    
    # 定义参数网格
    param_grid = [
        {
            'C': [0.1, 1, 10, 100],
            'kernel': ['linear']
        },
        {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf']
        },
        {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['poly'],
            'degree': [2, 3, 4]
        }
    ]
    
    # 使用网格搜索优化参数
    svm_model = SVC(random_state=42)
    grid_search = GridSearchCV(
        svm_model, 
        param_grid, 
        cv=5,  # 5折交叉验证
        scoring='accuracy',
        n_jobs=-1,  # 使用所有CPU核心
        verbose=1
    )
    
    print("执行网格搜索...")
    grid_search.fit(X_train, y_train)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
    
    return grid_search.best_estimator_, grid_search.best_params_

def evaluate_optimized_model(model, X_test, y_test):
    """评估优化后的模型"""
    print("\n评估优化后的模型...")
    
    # 预测
    prediction = model.predict(X_test)
    
    # 计算准确率
    accuracy = accuracy_score(y_test, prediction)
    print(f"优化后准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 详细分类报告
    test_labels = np.unique(y_test)
    test_target_names = [svm.display_names[i] if i < len(svm.display_names) else f"类别{i}" 
                        for i in test_labels]
    
    report = classification_report(y_test, prediction, 
                                 labels=test_labels,
                                 target_names=test_target_names,
                                 zero_division=0)
    print("\n详细分类报告:")
    print(report)
    
    # 计算各类别准确率
    category_accuracies = []
    category_names = []
    
    for label in test_labels:
        mask = y_test == label
        if np.sum(mask) > 0:
            acc = np.mean(prediction[mask] == y_test[mask])
            category_accuracies.append(acc)
            if label < len(svm.display_names):
                category_names.append(svm.display_names[label])
            else:
                category_names.append(f"类别{label}")
    
    return accuracy, category_names, category_accuracies, report

def generate_comparison_chart(category_names, category_accuracies, accuracy, best_params):
    """生成优化后的对比图"""
    print("\n生成优化后的准确率对比图...")
    
    if not category_accuracies:
        print("没有有效的分类结果")
        return
    
    # 排序
    sorted_indices = np.argsort(category_accuracies)[::-1]
    sorted_accuracies = [category_accuracies[i] for i in sorted_indices]
    sorted_names = [category_names[i] for i in sorted_indices]
    
    # 绘制图表
    plt.figure(figsize=(16, 10))
    colors = ['darkgreen' if acc >= 0.7 else 'green' if acc >= 0.5 else 'orange' if acc >= 0.3 else 'red' for acc in sorted_accuracies]
    
    bars = plt.barh(range(len(sorted_names)), sorted_accuracies, color=colors, alpha=0.8)
    
    # 添加数值标签
    for i, acc in enumerate(sorted_accuracies):
        plt.text(acc + 0.01, i, f'{acc:.3f}', va='center', fontsize=10, fontweight='bold')
    
    plt.yticks(range(len(sorted_names)), sorted_names)
    plt.xlabel('分类准确率', fontsize=12)
    plt.title('优化后各类别水果分类准确率对比', fontsize=16, fontweight='bold')
    plt.grid(axis='x', alpha=0.3)
    
    # 添加平均线
    plt.axvline(x=accuracy, color='blue', linestyle='--', linewidth=3, 
                label=f'整体准确率: {accuracy:.3f}')
    
    # 添加统计信息
    non_zero_count = sum(1 for acc in sorted_accuracies if acc > 0)
    good_count = sum(1 for acc in sorted_accuracies if acc >= 0.5)
    
    info_text = f'有效分类类别: {non_zero_count}/{len(sorted_names)}\n'
    info_text += f'良好分类类别(≥50%): {good_count}/{len(sorted_names)}\n'
    info_text += f'最佳参数: {best_params}'
    
    plt.text(0.6, len(sorted_names)-3, info_text,
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8),
             fontsize=10)
    
    plt.legend()
    plt.tight_layout()
    plt.savefig('优化后各类别分类准确率对比.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 优化后的准确率对比图已保存")

def save_optimization_results(accuracy, category_names, category_accuracies, report, best_params):
    """保存优化结果"""
    with open('模型优化结果报告.txt', 'w', encoding='utf-8') as f:
        f.write("SVM模型优化结果报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"最佳参数: {best_params}\n")
        f.write(f"优化后整体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n\n")
        
        f.write("各类别准确率:\n")
        f.write("-" * 40 + "\n")
        
        for name, acc in zip(category_names, category_accuracies):
            status = "优秀" if acc >= 0.7 else "良好" if acc >= 0.5 else "一般" if acc >= 0.3 else "较差"
            f.write(f"{name}: {acc:.3f} ({acc*100:.1f}%) - {status}\n")
        
        non_zero_count = sum(1 for acc in category_accuracies if acc > 0)
        good_count = sum(1 for acc in category_accuracies if acc >= 0.5)
        
        f.write(f"\n统计信息:\n")
        f.write(f"有效分类类别数: {non_zero_count}/{len(category_accuracies)}\n")
        f.write(f"良好分类类别数(≥50%): {good_count}/{len(category_accuracies)}\n")
        f.write(f"平均准确率: {np.mean(category_accuracies):.3f}\n\n")
        
        f.write("详细分类报告:\n")
        f.write("-" * 40 + "\n")
        f.write(report)
    
    print("✅ 优化结果报告已保存为 '模型优化结果报告.txt'")

def main():
    """主函数"""
    print("开始SVM模型优化...")
    print("=" * 60)
    
    try:
        # 准备数据
        X_train, X_test, y_train, y_test, scaler = prepare_data()
        
        # 优化参数
        best_model, best_params = optimize_svm_parameters(X_train, y_train)
        
        # 评估优化后的模型
        accuracy, category_names, category_accuracies, report = evaluate_optimized_model(
            best_model, X_test, y_test)
        
        # 生成对比图
        generate_comparison_chart(category_names, category_accuracies, accuracy, best_params)
        
        # 保存结果
        save_optimization_results(accuracy, category_names, category_accuracies, report, best_params)
        
        # 保存优化后的模型和预处理器
        model_path = os.path.join(svm.folder, 'optimized_model.sav')
        scaler_path = os.path.join(svm.folder, 'scaler.sav')
        
        pickle.dump(best_model, open(model_path, 'wb'))
        pickle.dump(scaler, open(scaler_path, 'wb'))
        
        print(f"\n🎉 模型优化完成！")
        print(f"优化后准确率: {accuracy*100:.2f}%")
        
        non_zero_count = sum(1 for acc in category_accuracies if acc > 0)
        good_count = sum(1 for acc in category_accuracies if acc >= 0.5)
        
        print(f"有效分类类别: {non_zero_count}/{len(category_accuracies)}")
        print(f"良好分类类别(≥50%): {good_count}/{len(category_accuracies)}")
        print(f"最佳参数: {best_params}")
        
        if good_count > len(category_accuracies) * 0.3:
            print("✅ 模型性能显著提升！")
        else:
            print("⚠️ 模型性能仍有提升空间，建议:")
            print("  1. 尝试更高级的特征提取方法（HOG、SIFT等）")
            print("  2. 使用深度学习方法")
            print("  3. 增加更多训练数据")
            print("  4. 尝试集成学习方法")
            
    except Exception as e:
        print(f"优化过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
