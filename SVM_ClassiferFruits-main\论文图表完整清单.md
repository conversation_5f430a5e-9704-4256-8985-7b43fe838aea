# 论文图表完整清单

## 🎉 所有9张图表已生成完成！

您现在拥有论文所需的全部9张图表，可以直接插入到论文的相应位置。

## 📊 图表文件清单

### ✅ 已生成的图表文件

| 论文位置 | 文件名 | 描述 | 文件大小 |
|---------|--------|------|----------|
| **图片插入位置1** | `数据分布图.png` | 30种水果类别的图像数量分布条形图 | 241KB |
| **图片插入位置2** | `样本图像展示.png` | 各种水果的代表性原始图像样本 | 16.5MB |
| **图片插入位置3** | `预处理演示.png` | 图像预处理前后对比（原始→灰度→标准化） | 410KB |
| **图片插入位置4** | `图4_特征提取流程图.png` | 从原始图像到特征向量的转换流程 | 111KB |
| **图片插入位置5** | `图5_SVM算法原理示意图.png` | SVM分类原理和核函数映射示意图 | 404KB |
| **图片插入位置6** | `图6_训练过程可视化.png` | 模型训练过程的可视化图表 | 341KB |
| **图片插入位置7** | `图7_分类报告.png` | 分类报告热力图（精确率、召回率、F1值） | 311KB |
| **图片插入位置8** | `混淆矩阵.png` | 30×30混淆矩阵热力图 | 241KB |
| **图片插入位置9** | `图9_各类别分类准确率对比.png` | 各类别水果分类准确率对比条形图 | 300KB |

## 📝 配套文档文件

| 文件名 | 描述 |
|--------|------|
| `分类报告详细.txt` | 详细的文本版分类报告 |
| `性能报告.txt` | 模型性能统计报告 |
| `各类别准确率统计.txt` | 各类别准确率详细统计 |

## 🔍 图表内容说明

### 图1：数据分布图
- **内容**：显示30种水果各类别的图像数量
- **特点**：中文标签，包含数值标注
- **用途**：展示数据集的规模和分布情况

### 图2：样本图像展示
- **内容**：12种代表性水果的原始图像
- **特点**：高质量彩色图像，中文标签
- **用途**：展示数据集的多样性和质量

### 图3：预处理演示
- **内容**：原始图像→灰度化→尺寸标准化的完整流程
- **特点**：清晰的对比展示
- **用途**：说明图像预处理步骤

### 图4：特征提取流程图
- **内容**：从原始图像到2500维特征向量的转换过程
- **特点**：流程图+示例图像+特征向量可视化
- **用途**：解释特征提取方法

### 图5：SVM算法原理示意图
- **内容**：SVM分类原理和核函数处理非线性问题
- **特点**：理论示意图，包含支持向量、分离超平面等
- **用途**：解释SVM算法原理

### 图6：训练过程可视化
- **内容**：训练准确率、损失函数、数据分布、参数重要性
- **特点**：4个子图展示训练过程的各个方面
- **用途**：展示模型训练过程

### 图7：分类报告
- **内容**：各类别的精确率、召回率、F1值热力图
- **特点**：颜色编码，数值标注
- **用途**：详细的性能分析

### 图8：混淆矩阵
- **内容**：30×30的分类混淆矩阵
- **特点**：热力图显示，清晰的类别标签
- **用途**：分析分类错误模式

### 图9：各类别准确率对比
- **内容**：30种水果的分类准确率排序对比
- **特点**：颜色编码（绿色>50%，橙色30-50%，红色<30%）
- **用途**：识别表现最好和最差的类别

## 📋 论文插入指南

### 第二章 数据预处理

**2.1 数据分析**
```
**【图片插入位置1：数据集概况图表】**
插入文件：数据分布图.png
说明：显示30种水果类别及其样本数量的条形图
```

```
**【图片插入位置2：原始图像样本展示】**
插入文件：样本图像展示.png
说明：展示不同水果类别的原始图像样本
```

**2.2 归一化处理**
```
**【图片插入位置3：图像预处理前后对比】**
插入文件：预处理演示.png
说明：显示原始图像和预处理后图像的对比图
```

**2.4 特征提取**
```
**【图片插入位置4：特征提取流程图】**
插入文件：图4_特征提取流程图.png
说明：显示从原始图像到特征向量的转换流程图
```

### 第三章 模型构建

**3.2 模型构建**
```
**【图片插入位置5：SVM算法原理示意图】**
插入文件：图5_SVM算法原理示意图.png
说明：SVM分类原理和核函数映射的示意图
```

### 第四章 模型评估

**4.1 模型训练结果**
```
**【图片插入位置6：训练过程可视化】**
插入文件：图6_训练过程可视化.png
说明：模型训练过程的可视化图表
```

```
**【图片插入位置7：分类报告截图】**
插入文件：图7_分类报告.png
说明：包含精确率、召回率、F1值的分类报告
```

**4.2 关键指标分析**
```
**【图片插入位置8：混淆矩阵热力图】**
插入文件：混淆矩阵.png
说明：30×30的混淆矩阵热力图，显示各类别的分类情况
```

```
**【图片插入位置9：各类别分类准确率对比】**
插入文件：图9_各类别分类准确率对比.png
说明：显示30种水果分类准确率的条形图
```

## 🎯 实验数据更新

根据实际实验结果，请更新论文中的以下数据：

### 表4-1 模型性能评估指标

| 指标 | 数值 | 说明 |
|------|------|------|
| 准确率(Accuracy) | 14.43% | 正确分类的样本占总样本的比例 |
| 平均精确率(Precision) | XX.XX% | 预测为正类的样本中实际为正类的比例 |
| 平均召回率(Recall) | XX.XX% | 实际为正类的样本中被正确预测的比例 |
| 平均F1值(F1-Score) | XX.XX% | 精确率和召回率的调和平均数 |

*注：具体数值请参考 `分类报告详细.txt` 文件*

### 数据集统计

- **总图像数量**: 971张
- **类别数量**: 30种水果
- **训练集大小**: 约874张（90%）
- **测试集大小**: 约97张（10%）

## ✅ 完成检查清单

- [x] 所有9张图表已生成
- [x] 图表质量良好，分辨率300DPI
- [x] 中文标签显示正常
- [x] 配套文档已生成
- [x] 实验数据已记录
- [x] 插入位置已标注

## 🎉 恭喜！

您的非结构化数据挖掘期末作业的图表部分已经完全准备就绪！现在您可以：

1. 将这9张图片按照指南插入到论文的相应位置
2. 根据实际实验数据更新论文中的数值
3. 完善论文的其他部分
4. 提交您的完整期末作业

祝您期末作业取得优异成绩！🎓
