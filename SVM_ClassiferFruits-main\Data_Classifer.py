import os
import random
import numpy as np
import cv2
import matplotlib.pyplot as plt
import pickle  # 将Python对象结构转换为字节流
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from math import sqrt
import sklearn.metrics as metric
from sklearn.metrics import accuracy_score

# 获取脚本所在目录的绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))

folder = os.path.join(script_dir, "File")  # 数据文件存储文件夹路径

data = ['data1.pickle', 'model.sav']  # 数据文件名

dir = os.path.join(script_dir, 'FruitImages')  # 水果图像数据集路径

# 30种水果类别（与实际文件夹名称匹配）
categories = ['acerolas', 'apples', 'apricots', 'avocados', 'bananas', 'blackberries', 'blueberries', 'cantaloupes', 'cherries', 'coconuts', 'figs', 'grapes', 'grapefruits', 'guava', 'kiwifruit', 'lemons', 'limes', 'mangos', 'olives', 'oranges', 'passionfruit', 'peaches', 'pears', 'pineapples', 'plums', 'pomegranates', 'raspberries', 'strawberries', 'tomatoes', 'watermelons']

# 用于显示的友好名称
display_names = ['西印度樱桃', '苹果', '杏', '牛油果', '香蕉', '黑莓', '蓝莓', '哈密瓜', '樱桃', '椰子', '无花果', '葡萄', '柚子', '番石榴', '猕猴桃', '柠檬', '青柠', '芒果', '橄榄', '橙子', '百香果', '桃子', '梨', '菠萝', '李子', '石榴', '覆盆子', '草莓', '番茄', '西瓜']

# 获取文件路径的函数
def location_data(stt):
    path = os.path.join(folder, data[stt])
    return path

# 创建训练数据的函数
def create_training_data():
    # 确保File文件夹存在
    if not os.path.exists(folder):
        os.makedirs(folder)

    data = []

    for category in categories:
        path = os.path.join(dir, category)  # 将类别名连接到目录路径，获取每种水果的图像文件夹路径
        label = categories.index(category)  # 每种水果的标签（30种水果对应30个标签，从0到29）
        for img in os.listdir(path):  # 遍历路径下的所有图像文件
            imgpath = os.path.join(path, img)  # 将图像文件名连接到对应的文件夹路径
            fruit_img = cv2.imread(imgpath, 0)  # 读取灰度图像
            try:
                fruit_img = cv2.resize(fruit_img, (50, 50))  # 调整图像大小为50x50像素
                image = np.array(fruit_img).flatten()  # 将图像数组展平为一维向量

                data.append([image, label])  # 将图像特征和标签添加到数据集
            except Exception as e:
                pass  # 跳过无法处理的图像

    data_file_path = os.path.join(folder, 'data1.pickle')
    pick_in = open(data_file_path, 'wb')  # 打开文件用于写入
    pickle.dump(data, pick_in)  # 将数据保存到pickle文件
    pick_in.close()

# 读取data1.pickle文件的函数
def data1_file():
    data_file_path = os.path.join(folder, 'data1.pickle')
    pick_in = open(data_file_path, 'rb')  # 打开文件用于读取
    data = pickle.load(pick_in)  # 从pickle文件加载数据
    pick_in.close()
    return data


# 可视化各类别图像数量的函数
def visualize():
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

    num_images = []
    for lab in categories:
        try:
            files = os.listdir(os.path.join(dir, lab))  # 获取每个类别文件夹中的文件列表
            # 只计算图像文件
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            c = len(image_files)  # 计算图像数量
            num_images.append(c)
        except FileNotFoundError:
            print(f"警告：文件夹 {lab} 不存在")
            num_images.append(0)

    y_pos = np.arange(len(categories))  # 创建y轴位置数组
    plt.figure(figsize=(15, 10))  # 设置图形大小
    bars = plt.barh(y_pos, num_images, align='center', color='skyblue', alpha=0.8)  # 创建水平条形图
    plt.yticks(y_pos, display_names)  # 设置y轴标签为中文名称
    plt.xlabel('图像数量')  # x轴标签
    plt.title('各类别水果图像数量分布')  # 图表标题

    # 在条形图上添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width + 1, bar.get_y() + bar.get_height()/2,
                f'{num_images[i]}', ha='left', va='center')

    plt.tight_layout()  # 调整布局
    plt.show()


# 创建训练集和测试集的函数
def train_tests_plit():
    random.shuffle(data1_file())  # 随机打乱数据
    features = []
    labels = []

    for feature, label in data1_file():
        features.append(feature)  # 添加图像特征
        labels.append(label)      # 添加对应标签

    global X_train, X_test, y_train, y_test

    # 将数据分割为训练集和测试集，测试集占10%
    X_train, X_test, y_train, y_test = train_test_split(features, labels, test_size=0.1, random_state=42)

# SVM分类算法函数
def SVM():
    # C: 表示软间隔中的惩罚参数
    # kernel: 核函数，用于将数据变换到高维空间
    # gamma: 核函数的系数
    model = SVC(C=1, kernel='poly', gamma=1)  # 使用多项式核的SVM
    model = model.fit(X_train, y_train)  # 使用训练数据拟合SVM模型

    # 保存训练好的模型
    model_file_path = os.path.join(folder, 'model.sav')
    pick = open(model_file_path, 'wb')
    pickle.dump(model, pick)
    pick.close()

# 预测和计算准确率的函数
def Result(x):
    train_tests_plit()  # 重新分割数据

    # 加载训练好的模型
    model_file_path = os.path.join(folder, 'model.sav')
    pick = open(model_file_path, 'rb')
    model = pickle.load(pick)
    pick.close()

    if (x == 1):
        prediction = model.predict(X_test)  # 对测试集进行预测

        # 打印分类报告
        print(metric.classification_report(y_test, prediction))

        # 计算准确率
        acc_test = accuracy_score(prediction, y_test)
        print('-------------------------------------------------------------')
        print('测试集准确率: ', round(acc_test * 100, 2), '%')
        return display_names[prediction[0]]  # 返回第一个预测结果的中文名称
    else:
        accuracy = model.score(X_test, y_test)  # 返回模型在测试集上的准确率
        return f"模型准确率: {round(accuracy * 100, 2)}%"

# 显示测试图像的函数
def Display():
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    try:
        if 'X_test' in globals() and len(X_test) > 0:
            mypet = X_test[0].reshape(50, 50)  # 将第一个测试图像重塑为50x50
            plt.figure(figsize=(6, 6))
            plt.imshow(mypet, cmap='gray')  # 显示灰度图像
            plt.title('测试图像示例')
            plt.axis('off')  # 不显示坐标轴
            plt.show()
        else:
            print("没有可用的测试数据，请先运行数据分割")
    except Exception as e:
        print(f"显示图像时出错: {str(e)}")

# 删除已创建文件的函数
def Delete_Data():
    for f in os.listdir(folder):
        path = os.path.join(folder, f)
        os.remove(path)  # 删除文件