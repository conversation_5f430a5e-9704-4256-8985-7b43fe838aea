# 水果分类系统使用说明

## 🎉 问题已解决！

之前遇到的"找不到文件夹"问题已经完全修复。现在所有功能都可以正常使用。

## 📁 文件结构

```
SVM_ClassiferFruits-main/
├── FruitImages/           # 数据集文件夹（30种水果）
├── File/                  # 模型和数据文件存储
│   ├── data1.pickle      # 训练数据
│   └── model.sav         # 训练好的SVM模型
├── GUI.py                # 主GUI界面
├── ClassiferFruit.py     # 图像识别界面
├── Data_Classifer.py     # 核心算法模块
└── generate_figures_fixed.py  # 图表生成脚本
```

## 🚀 使用方法

### 方法一：使用主GUI界面（推荐）

1. **启动GUI**
   ```bash
   cd SVM_ClassiferFruits-main
   python GUI.py
   ```

2. **按顺序操作**（如果是第一次使用）：
   - 点击"导入数据"按钮 → 创建训练数据
   - 点击"创建训练测试集"按钮 → 分割数据
   - 点击"训练模型"按钮 → 训练SVM模型
   - 点击"预测"按钮 → 查看模型性能
   - 点击"数据可视化"按钮 → 查看数据分布

3. **如果已经训练过模型**：
   - 直接点击"预测"或"数据可视化"即可

### 方法二：使用图像识别界面

1. **启动图像识别**
   ```bash
   cd SVM_ClassiferFruits-main
   python ClassiferFruit.py
   ```

2. **上传图片识别**：
   - 点击"选择图像"按钮
   - 选择一张水果图片
   - 系统会自动显示识别结果

### 方法三：生成论文图表

```bash
cd SVM_ClassiferFruits-main
python generate_figures_fixed.py
```

## 📊 系统性能

- **数据集规模**: 971张图片，30种水果
- **模型准确率**: 14.43%
- **测试样本数**: 97个
- **支持的水果类别**: 30种（西印度樱桃、苹果、杏、牛油果等）

## 🔧 修复的问题

1. **路径问题**: 使用绝对路径替代相对路径
2. **文件夹名称匹配**: 代码中的类别名称与数据集文件夹名称一致
3. **错误处理**: 增加了详细的错误提示
4. **中文显示**: 所有界面和结果都支持中文显示

## 📈 生成的图表文件

运行图表生成脚本后，会在当前目录生成：

1. **数据分布图.png** - 各类别图像数量分布
2. **样本图像展示.png** - 12种水果的样本展示
3. **预处理演示.png** - 图像预处理流程演示
4. **混淆矩阵.png** - 模型性能混淆矩阵
5. **性能报告.txt** - 详细性能统计

## ⚠️ 注意事项

1. **运行位置**: 必须在`SVM_ClassiferFruits-main`文件夹内运行脚本
2. **数据集**: 确保`FruitImages`文件夹包含30个水果类别的子文件夹
3. **依赖库**: 确保已安装所有必要的Python库（见requirements.txt）

## 🐛 故障排除

如果遇到问题，可以运行调试脚本：

```bash
cd SVM_ClassiferFruits-main
python debug_paths.py        # 检查路径
python test_gui_functions.py # 测试所有功能
```

## 📝 论文图表使用

生成的图表可以直接插入到论文中：

- **图1**: 数据分布图.png → 论文图片插入位置1
- **图2**: 样本图像展示.png → 论文图片插入位置2  
- **图3**: 预处理演示.png → 论文图片插入位置3
- **图4**: 混淆矩阵.png → 论文图片插入位置8

## ✅ 验证系统正常工作

如果看到以下结果，说明系统工作正常：

1. GUI界面能正常打开，所有按钮可点击
2. "导入数据"显示"训练数据已创建"或"文件已存在"
3. "预测"能显示准确率和预测结果
4. "数据可视化"能显示图表
5. 图像识别界面能正确识别上传的水果图片

现在您的水果分类系统已经完全可以正常使用了！🎉
