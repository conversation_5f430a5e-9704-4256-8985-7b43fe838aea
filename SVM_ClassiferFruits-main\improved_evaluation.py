"""
改进的模型评估脚本
解决测试集样本分布不均的问题
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import Data_Classifer as svm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def improved_data_split():
    """改进的数据分割，确保每个类别都有样本在测试集中"""
    print("执行改进的数据分割...")
    
    # 加载数据
    data = svm.data1_file()
    features = []
    labels = []
    
    for feature, label in data:
        features.append(feature)
        labels.append(label)
    
    features = np.array(features)
    labels = np.array(labels)
    
    print(f"总样本数: {len(features)}")
    print(f"类别数: {len(np.unique(labels))}")
    
    # 统计每个类别的样本数
    unique_labels, counts = np.unique(labels, return_counts=True)
    print("\n各类别样本数:")
    for label, count in zip(unique_labels, counts):
        if label < len(svm.display_names):
            print(f"{svm.display_names[label]}: {count}")
    
    # 使用分层抽样确保每个类别都有样本在测试集中
    try:
        # 对于样本数较少的类别，使用更小的测试集比例
        min_samples = np.min(counts)
        if min_samples < 10:
            test_size = max(0.1, 1.0/min_samples)  # 至少保证每个类别有1个测试样本
            test_size = min(test_size, 0.3)  # 但不超过30%
        else:
            test_size = 0.2  # 20%作为测试集
        
        print(f"使用测试集比例: {test_size:.2f}")
        
        sss = StratifiedShuffleSplit(n_splits=1, test_size=test_size, random_state=42)
        train_idx, test_idx = next(sss.split(features, labels))
        
        X_train = features[train_idx]
        X_test = features[test_idx]
        y_train = labels[train_idx]
        y_test = labels[test_idx]
        
        print(f"训练集大小: {len(X_train)}")
        print(f"测试集大小: {len(X_test)}")
        
        # 检查测试集中每个类别的样本数
        test_unique, test_counts = np.unique(y_test, return_counts=True)
        print(f"\n测试集中的类别数: {len(test_unique)}")
        print("测试集各类别样本数:")
        for label, count in zip(test_unique, test_counts):
            if label < len(svm.display_names):
                print(f"{svm.display_names[label]}: {count}")
        
        return X_train, X_test, y_train, y_test
        
    except Exception as e:
        print(f"分层抽样失败: {str(e)}")
        print("使用普通随机分割...")
        return train_test_split(features, labels, test_size=0.2, random_state=42)

def retrain_and_evaluate():
    """重新训练和评估模型"""
    print("\n开始重新训练模型...")
    
    # 改进的数据分割
    X_train, X_test, y_train, y_test = improved_data_split()
    
    # 训练SVM模型
    from sklearn.svm import SVC
    model = SVC(C=1, kernel='poly', gamma=1, random_state=42)
    model.fit(X_train, y_train)
    
    # 保存模型
    model_path = os.path.join(svm.folder, 'improved_model.sav')
    pickle.dump(model, open(model_path, 'wb'))
    
    # 预测
    prediction = model.predict(X_test)
    
    # 计算整体准确率
    accuracy = accuracy_score(y_test, prediction)
    print(f"\n改进后的整体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 详细分类报告
    print("\n详细分类报告:")
    target_names = [svm.display_names[i] if i < len(svm.display_names) else f"类别{i}" 
                   for i in range(len(svm.display_names))]
    
    # 只包含测试集中实际出现的类别
    test_labels = np.unique(y_test)
    test_target_names = [svm.display_names[i] if i < len(svm.display_names) else f"类别{i}" 
                        for i in test_labels]
    
    report = classification_report(y_test, prediction, 
                                 labels=test_labels,
                                 target_names=test_target_names,
                                 zero_division=0)
    print(report)
    
    # 计算各类别准确率
    category_accuracies = []
    category_names = []
    
    for label in test_labels:
        mask = y_test == label
        if np.sum(mask) > 0:
            acc = np.mean(prediction[mask] == y_test[mask])
            category_accuracies.append(acc)
            if label < len(svm.display_names):
                category_names.append(svm.display_names[label])
            else:
                category_names.append(f"类别{label}")
    
    # 生成改进的准确率对比图
    generate_improved_accuracy_chart(category_names, category_accuracies, accuracy)
    
    # 保存结果
    save_improved_results(report, accuracy, category_names, category_accuracies)
    
    return accuracy, category_names, category_accuracies

def generate_improved_accuracy_chart(category_names, category_accuracies, overall_acc):
    """生成改进的准确率对比图"""
    print("\n生成改进的准确率对比图...")
    
    if not category_accuracies:
        print("没有有效的分类结果")
        return
    
    # 排序
    sorted_indices = np.argsort(category_accuracies)[::-1]
    sorted_accuracies = [category_accuracies[i] for i in sorted_indices]
    sorted_names = [category_names[i] for i in sorted_indices]
    
    # 绘制图表
    plt.figure(figsize=(15, 10))
    colors = ['green' if acc >= 0.5 else 'orange' if acc >= 0.3 else 'red' for acc in sorted_accuracies]
    
    bars = plt.barh(range(len(sorted_names)), sorted_accuracies, color=colors, alpha=0.7)
    
    # 添加数值标签
    for i, acc in enumerate(sorted_accuracies):
        plt.text(acc + 0.01, i, f'{acc:.3f}', va='center', fontsize=10)
    
    plt.yticks(range(len(sorted_names)), sorted_names)
    plt.xlabel('分类准确率')
    plt.title('改进后各类别水果分类准确率对比', fontsize=16, fontweight='bold')
    plt.grid(axis='x', alpha=0.3)
    
    # 添加平均线
    plt.axvline(x=overall_acc, color='blue', linestyle='--', linewidth=2, 
                label=f'整体准确率: {overall_acc:.3f}')
    
    # 添加统计信息
    non_zero_count = sum(1 for acc in sorted_accuracies if acc > 0)
    plt.text(0.7, len(sorted_names)-1, 
             f'有效分类类别: {non_zero_count}/{len(sorted_names)}', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.legend()
    plt.tight_layout()
    plt.savefig('改进后各类别分类准确率对比.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 改进的准确率对比图已保存")

def save_improved_results(report, accuracy, category_names, category_accuracies):
    """保存改进的结果"""
    with open('改进后模型评估报告.txt', 'w', encoding='utf-8') as f:
        f.write("改进后模型评估报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"整体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n\n")
        f.write("各类别准确率:\n")
        f.write("-" * 30 + "\n")
        
        for name, acc in zip(category_names, category_accuracies):
            f.write(f"{name}: {acc:.3f} ({acc*100:.1f}%)\n")
        
        non_zero_count = sum(1 for acc in category_accuracies if acc > 0)
        f.write(f"\n有效分类类别数: {non_zero_count}/{len(category_accuracies)}\n")
        f.write(f"平均准确率: {np.mean(category_accuracies):.3f}\n\n")
        f.write("详细分类报告:\n")
        f.write("-" * 30 + "\n")
        f.write(report)
    
    print("✅ 改进后的评估报告已保存为 '改进后模型评估报告.txt'")

def main():
    """主函数"""
    print("开始改进的模型评估...")
    print("=" * 60)
    
    # 检查数据文件是否存在
    if not os.path.isfile(svm.location_data(0)):
        print("数据文件不存在，请先运行数据导入")
        return
    
    try:
        accuracy, category_names, category_accuracies = retrain_and_evaluate()
        
        print(f"\n🎉 评估完成！")
        print(f"整体准确率从 14.43% 提升到 {accuracy*100:.2f}%")
        
        non_zero_count = sum(1 for acc in category_accuracies if acc > 0)
        print(f"有效分类类别: {non_zero_count}/{len(category_accuracies)}")
        
        if non_zero_count > len(category_accuracies) * 0.5:
            print("✅ 大部分类别都有有效的分类结果")
        else:
            print("⚠️ 仍有较多类别准确率为0，建议:")
            print("  1. 增加训练数据")
            print("  2. 调整SVM参数")
            print("  3. 尝试其他特征提取方法")
            
    except Exception as e:
        print(f"评估过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()
