"""
测试各个功能是否正常工作
"""

import os
import sys
import Data_Classifer as svm

def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载功能 ===")
    
    try:
        # 测试可视化功能
        print("测试数据可视化...")
        svm.visualize()
        print("✅ 数据可视化成功")
        return True
    except Exception as e:
        print(f"❌ 数据可视化失败: {str(e)}")
        return False

def test_data_creation():
    """测试数据创建功能"""
    print("\n=== 测试数据创建功能 ===")
    
    try:
        # 检查是否已有数据文件
        if os.path.exists(svm.location_data(0)):
            print("✅ 数据文件已存在")
        else:
            print("创建训练数据...")
            svm.create_training_data()
            print("✅ 训练数据创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据创建失败: {str(e)}")
        return False

def test_data_splitting():
    """测试数据分割功能"""
    print("\n=== 测试数据分割功能 ===")
    
    try:
        print("分割训练测试集...")
        svm.train_tests_plit()
        print("✅ 数据分割成功")
        return True
    except Exception as e:
        print(f"❌ 数据分割失败: {str(e)}")
        return False

def test_model_training():
    """测试模型训练功能"""
    print("\n=== 测试模型训练功能 ===")
    
    try:
        if os.path.exists(svm.location_data(1)):
            print("✅ 模型文件已存在")
        else:
            print("训练SVM模型...")
            svm.SVM()
            print("✅ 模型训练成功")
        return True
    except Exception as e:
        print(f"❌ 模型训练失败: {str(e)}")
        return False

def test_prediction():
    """测试预测功能"""
    print("\n=== 测试预测功能 ===")
    
    try:
        print("测试模型预测...")
        accuracy = svm.Result(2)
        prediction = svm.Result(1)
        
        print(f"✅ 模型准确率: {accuracy}")
        print(f"✅ 预测示例: {prediction}")
        return True
    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")
        return False

def test_display():
    """测试图像显示功能"""
    print("\n=== 测试图像显示功能 ===")
    
    try:
        print("显示测试图像...")
        svm.Display()
        print("✅ 图像显示成功")
        return True
    except Exception as e:
        print(f"❌ 图像显示失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始功能测试...")
    print("=" * 50)
    
    results = []
    
    # 按顺序测试各个功能
    results.append(test_data_loading())
    results.append(test_data_creation())
    results.append(test_data_splitting())
    results.append(test_model_training())
    results.append(test_prediction())
    results.append(test_display())
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    test_names = [
        "数据可视化",
        "数据创建", 
        "数据分割",
        "模型训练",
        "模型预测",
        "图像显示"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有功能正常！")
    else:
        print("⚠️ 部分功能存在问题，请检查错误信息")

if __name__ == "__main__":
    main()
