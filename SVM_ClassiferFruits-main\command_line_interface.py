"""
命令行界面版本的水果分类系统
不依赖GUI，通过命令行操作所有功能
"""

import os
import sys
import Data_Classifer as svm

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🍎 水果分类系统 - 命令行版本")
    print("="*50)
    print("1. 导入数据 (创建训练数据)")
    print("2. 创建训练测试集")
    print("3. 训练SVM模型")
    print("4. 测试模型预测")
    print("5. 数据可视化")
    print("6. 显示测试图像")
    print("7. 生成论文图表")
    print("8. 查看系统状态")
    print("9. 删除所有文件")
    print("0. 退出")
    print("="*50)

def show_status():
    """显示系统状态"""
    print("\n📊 系统状态:")
    print("-" * 30)
    
    # 检查数据文件
    data_exists = os.path.exists(svm.location_data(0))
    print(f"训练数据文件: {'✅ 存在' if data_exists else '❌ 不存在'}")
    
    # 检查模型文件
    model_exists = os.path.exists(svm.location_data(1))
    print(f"模型文件: {'✅ 存在' if model_exists else '❌ 不存在'}")
    
    # 检查数据集文件夹
    dataset_exists = os.path.exists(svm.dir)
    print(f"数据集文件夹: {'✅ 存在' if dataset_exists else '❌ 不存在'}")
    
    if dataset_exists:
        # 统计图像数量
        total_images = 0
        valid_categories = 0
        for category in svm.categories:
            try:
                category_path = os.path.join(svm.dir, category)
                if os.path.exists(category_path):
                    files = os.listdir(category_path)
                    image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                    count = len(image_files)
                    if count > 0:
                        total_images += count
                        valid_categories += 1
            except:
                pass
        
        print(f"有效类别数: {valid_categories}/30")
        print(f"总图像数: {total_images}")
    
    print("-" * 30)

def import_data():
    """导入数据功能"""
    print("\n🔄 正在导入数据...")
    
    if os.path.isfile(svm.location_data(0)):
        print("⚠️ 训练数据文件已存在!")
        choice = input("是否重新创建? (y/n): ").lower()
        if choice != 'y':
            return
    
    try:
        svm.create_training_data()
        print("✅ 训练数据创建成功!")
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")

def create_train_test():
    """创建训练测试集"""
    print("\n🔄 正在创建训练测试集...")
    
    if not os.path.isfile(svm.location_data(0)):
        print("❌ 训练数据文件不存在，请先导入数据!")
        return
    
    try:
        svm.train_tests_plit()
        print("✅ 训练测试集创建成功!")
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")

def train_model():
    """训练模型"""
    print("\n🔄 正在训练SVM模型...")
    
    if os.path.isfile(svm.location_data(1)):
        print("⚠️ 模型文件已存在!")
        choice = input("是否重新训练? (y/n): ").lower()
        if choice != 'y':
            return
    
    try:
        # 确保有训练数据
        if not os.path.isfile(svm.location_data(0)):
            print("创建训练数据...")
            svm.create_training_data()
        
        # 分割数据
        print("分割训练测试集...")
        svm.train_tests_plit()
        
        # 训练模型
        print("训练模型中...")
        svm.SVM()
        print("✅ 模型训练成功!")
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")

def test_prediction():
    """测试预测"""
    print("\n🔄 正在测试模型预测...")
    
    if not os.path.isfile(svm.location_data(1)):
        print("❌ 模型文件不存在，请先训练模型!")
        return
    
    try:
        print("计算模型准确率...")
        accuracy = svm.Result(2)
        print(f"📊 {accuracy}")
        
        print("\n获取预测示例...")
        prediction = svm.Result(1)
        print(f"🎯 预测示例: {prediction}")
        
    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")

def visualize_data():
    """数据可视化"""
    print("\n🔄 正在生成数据可视化...")
    
    try:
        svm.visualize()
        print("✅ 数据可视化完成!")
    except Exception as e:
        print(f"❌ 可视化失败: {str(e)}")

def display_image():
    """显示测试图像"""
    print("\n🔄 正在显示测试图像...")
    
    try:
        svm.Display()
        print("✅ 图像显示完成!")
    except Exception as e:
        print(f"❌ 显示失败: {str(e)}")

def generate_figures():
    """生成论文图表"""
    print("\n🔄 正在生成论文图表...")
    
    try:
        # 导入图表生成模块
        import generate_figures_fixed
        generate_figures_fixed.main()
        print("✅ 论文图表生成完成!")
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")

def delete_files():
    """删除文件"""
    print("\n⚠️ 删除所有训练文件")
    
    if not os.path.exists(svm.folder) or not os.listdir(svm.folder):
        print("📁 文件夹为空，无需删除")
        return
    
    print("这将删除以下文件:")
    for f in os.listdir(svm.folder):
        print(f"  - {f}")
    
    choice = input("\n确认删除? (y/n): ").lower()
    if choice == 'y':
        try:
            svm.Delete_Data()
            print("✅ 文件删除成功!")
        except Exception as e:
            print(f"❌ 删除失败: {str(e)}")
    else:
        print("❌ 取消删除")

def main():
    """主函数"""
    print("🍎 欢迎使用水果分类系统!")
    print("这是命令行版本，所有功能都可以通过菜单操作")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-9): ").strip()
            
            if choice == '1':
                import_data()
            elif choice == '2':
                create_train_test()
            elif choice == '3':
                train_model()
            elif choice == '4':
                test_prediction()
            elif choice == '5':
                visualize_data()
            elif choice == '6':
                display_image()
            elif choice == '7':
                generate_figures()
            elif choice == '8':
                show_status()
            elif choice == '9':
                delete_files()
            elif choice == '0':
                print("\n👋 感谢使用水果分类系统!")
                break
            else:
                print("❌ 无效选择，请输入0-9之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
