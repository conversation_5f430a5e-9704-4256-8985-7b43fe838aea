"""
单独生成图9：各类别分类准确率对比
修复索引问题
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
import Data_Classifer as svm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def generate_figure_9():
    """生成图9：各类别分类准确率对比"""
    print("生成图9：各类别分类准确率对比...")

    # 确保模型存在
    if not os.path.isfile(svm.location_data(1)):
        print("模型不存在，请先训练模型")
        return

    # 获取预测结果
    svm.train_tests_plit()
    model = pickle.load(open(svm.location_data(1), 'rb'))
    prediction = model.predict(svm.X_test)

    # 计算各类别准确率
    unique_labels = np.unique(svm.y_test)
    category_accuracies = []
    category_names = []

    # 转换为numpy数组以便索引
    y_test_array = np.array(svm.y_test)
    prediction_array = np.array(prediction)

    for label in unique_labels:
        mask = y_test_array == label
        if np.sum(mask) > 0:
            acc = np.mean(prediction_array[mask] == y_test_array[mask])
            category_accuracies.append(acc)
            # 确保索引在范围内
            if label < len(svm.display_names):
                category_names.append(svm.display_names[label])
            else:
                category_names.append(f"类别{label}")

    if not category_accuracies:
        print("没有找到有效的分类结果")
        return

    # 排序
    sorted_indices = np.argsort(category_accuracies)[::-1]
    sorted_accuracies = [category_accuracies[i] for i in sorted_indices]
    sorted_names = [category_names[i] for i in sorted_indices]

    # 绘制条形图
    plt.figure(figsize=(15, 10))
    colors = ['green' if acc >= 0.5 else 'orange' if acc >= 0.3 else 'red' for acc in sorted_accuracies]

    bars = plt.barh(range(len(sorted_names)), sorted_accuracies, color=colors, alpha=0.7)

    # 添加数值标签
    for i, (bar, acc) in enumerate(zip(bars, sorted_accuracies)):
        plt.text(acc + 0.01, i, f'{acc:.3f}', va='center', fontsize=10)

    plt.yticks(range(len(sorted_names)), sorted_names)
    plt.xlabel('分类准确率')
    plt.title('各类别水果分类准确率对比', fontsize=16, fontweight='bold')
    plt.grid(axis='x', alpha=0.3)

    # 添加平均线
    avg_acc = np.mean(sorted_accuracies)
    plt.axvline(x=avg_acc, color='red', linestyle='--', linewidth=2,
                label=f'平均准确率: {avg_acc:.3f}')
    plt.legend()

    plt.tight_layout()
    plt.savefig('图9_各类别分类准确率对比.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 图9已保存")

    # 保存详细统计
    with open('各类别准确率统计.txt', 'w', encoding='utf-8') as f:
        f.write("各类别分类准确率统计\n")
        f.write("=" * 40 + "\n\n")
        for name, acc in zip(sorted_names, sorted_accuracies):
            f.write(f"{name}: {acc:.3f} ({acc*100:.1f}%)\n")
        f.write(f"\n平均准确率: {avg_acc:.3f} ({avg_acc*100:.1f}%)\n")

    print("详细统计已保存为 '各类别准确率统计.txt'")

if __name__ == "__main__":
    generate_figure_9()
