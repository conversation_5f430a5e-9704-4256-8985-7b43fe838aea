import os
from tkinter import *
from tkinter import messagebox, ttk
import Data_Classifer as svm

a = Tk()  # 创建GUI应用程序的主窗口

a.title("水果分类系统")

a.geometry('600x300')  # 设置窗口大小


# 检查data1.pickle文件是否已创建的函数
def check_create_training_data():
    if(not os.path.isfile(svm.location_data(0))):  # 如果data1.pickle文件不存在
        try:
            svm.create_training_data()
            messagebox.showinfo('通知', '训练数据已创建')
        except Exception as e:
            messagebox.showerror('通知', '创建文件失败!')
    else:
        messagebox.showwarning('通知', '文件已存在!')

# 检查数据分割为训练集和测试集的函数
def check_train_tests_plit():
    if(os.path.isfile(svm.location_data(0))):  # 如果data1.pickle文件已创建
        try:
            svm.train_tests_plit()
            messagebox.showinfo('通知', '训练集和测试集已创建')
        except Exception as e:
            messagebox.showerror('通知', '数据分割失败!')
    else:
        messagebox.showwarning('通知', '还没有图像数据！请先导入数据')

# SVM模型训练函数
def SVM():
    if (not os.path.isfile(svm.location_data(1))):
        try:
            svm.SVM()
            messagebox.showinfo('通知', '模型训练已完成!')
        except Exception as e:
            messagebox.showerror('通知', '训练数据尚未创建')
    else:
        messagebox.showwarning('通知', '训练模型已存在!')

# 预测和显示准确率的函数
def Result():
    if (os.path.isfile(svm.location_data(1))):
        # 显示准确率
        acc = Label(a, background='yellow', width=20, font=('Time New Roman', 10), text=svm.Result(2))
        # 显示预测结果
        pred = Label(a, background='yellow', width=20, font=('Time New Roman', 10), text=svm.Result(1))
        acc.place(x=220, y=170)
        pred.place(x=220, y=220)

        svm.Display()  # 显示测试图像
    else:
        messagebox.showerror('通知', '预测失败')

# 删除已创建数据文件的函数
def delete_file():
    if(os.listdir(svm.folder)):  # 如果File文件夹中存在文件
        svm.Delete_Data()
        messagebox.showinfo('通知', '模型已删除，现在可以训练新模型!')
    else:
        messagebox.showwarning('通知', '文件夹为空!')


# 主窗口标题标签
label = Label(a, width=40, font=('Time New Roman', 20), fg='red', text='水果分类系统', bg='yellow')
label.place(y=1)

# 导入数据按钮
btn = Button(a, text='导入数据', width=10, font=('Time New Roman', 10), activebackground='Light Blue', command=check_create_training_data)
btn.place(x=50, y=70)

# 创建训练测试数据按钮
btn1 = Button(a, text='创建训练测试集', width=17, font=('Time New Roman', 10), activebackground='Light Blue', command=check_train_tests_plit)
btn1.place(x=230, y=70)

# 训练模型按钮
btn2 = Button(a, text='训练模型', width=17, font=('Time New Roman', 10), activebackground='Light Blue', command=SVM)
btn2.place(x=230, y=120)

# 预测按钮
btn3 = Button(a, text='预测', width=10, font=('Time New Roman', 10), activebackground='Light Blue', command=Result)
btn3.place(x=450, y=70)

# 数据可视化按钮
btn5 = Button(a, text='数据可视化', width=10, font=('Time New Roman', 10), activebackground='Light Blue', command=svm.visualize)
btn5.place(x=450, y=120)

# 删除文件按钮
btn4 = Button(a, text='删除文件', width=10, font=('Time New Roman', 10), activebackground='Light Blue', command=delete_file)
btn4.place(x=50, y=120)

a.mainloop()  # 启动GUI主事件循环