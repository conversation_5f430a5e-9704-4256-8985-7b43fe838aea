"""
生成论文所需图表的脚本
运行此脚本可以生成论文中需要插入的所有图表
"""

import os
import pickle
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, precision_score, recall_score, f1_score, accuracy_score
import Data_Classifer as svm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def generate_data_distribution():
    """生成数据分布图表 - 论文图片位置1"""
    print("正在生成数据分布图表...")
    
    num_images = []
    for lab in svm.categories:
        try:
            files = os.listdir(os.path.join(svm.dir, lab))
            c = len(files)
            num_images.append(c)
        except:
            num_images.append(0)
    
    plt.figure(figsize=(15, 10))
    y_pos = np.arange(len(svm.categories))
    bars = plt.barh(y_pos, num_images, align='center', color='skyblue', alpha=0.8)
    plt.yticks(y_pos, svm.categories)
    plt.xlabel('图像数量')
    plt.title('FIDS30数据集各类别图像数量分布', fontsize=16, fontweight='bold')
    plt.grid(axis='x', alpha=0.3)
    
    # 在条形图上添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width + 1, bar.get_y() + bar.get_height()/2, 
                f'{num_images[i]}', ha='left', va='center')
    
    plt.tight_layout()
    plt.savefig('图1_数据分布.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("数据分布图表已保存为 '图1_数据分布.png'")

def generate_sample_images():
    """生成样本图像展示 - 论文图片位置2"""
    print("正在生成样本图像展示...")
    
    fig, axes = plt.subplots(6, 5, figsize=(20, 24))
    fig.suptitle('FIDS30数据集样本图像展示', fontsize=20, fontweight='bold')
    
    for i, category in enumerate(svm.categories):
        row = i // 5
        col = i % 5
        
        try:
            # 获取该类别的第一张图片
            category_path = os.path.join(svm.dir, category)
            if os.path.exists(category_path):
                images = os.listdir(category_path)
                if images:
                    img_path = os.path.join(category_path, images[0])
                    img = cv2.imread(img_path)
                    if img is not None:
                        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        axes[row, col].imshow(img_rgb)
                        axes[row, col].set_title(category, fontsize=12, fontweight='bold')
                        axes[row, col].axis('off')
                    else:
                        axes[row, col].text(0.5, 0.5, f'{category}\n(图像加载失败)', 
                                          ha='center', va='center', transform=axes[row, col].transAxes)
                        axes[row, col].axis('off')
                else:
                    axes[row, col].text(0.5, 0.5, f'{category}\n(无图像)', 
                                      ha='center', va='center', transform=axes[row, col].transAxes)
                    axes[row, col].axis('off')
            else:
                axes[row, col].text(0.5, 0.5, f'{category}\n(文件夹不存在)', 
                                  ha='center', va='center', transform=axes[row, col].transAxes)
                axes[row, col].axis('off')
        except Exception as e:
            axes[row, col].text(0.5, 0.5, f'{category}\n(错误: {str(e)})', 
                              ha='center', va='center', transform=axes[row, col].transAxes)
            axes[row, col].axis('off')
    
    plt.tight_layout()
    plt.savefig('图2_样本图像展示.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("样本图像展示已保存为 '图2_样本图像展示.png'")

def generate_preprocessing_comparison():
    """生成图像预处理对比 - 论文图片位置3"""
    print("正在生成图像预处理对比...")
    
    # 尝试找到一张示例图片
    sample_img_path = None
    for category in svm.categories[:5]:  # 只检查前5个类别
        category_path = os.path.join(svm.dir, category)
        if os.path.exists(category_path):
            images = os.listdir(category_path)
            if images:
                sample_img_path = os.path.join(category_path, images[0])
                break
    
    if sample_img_path and os.path.exists(sample_img_path):
        # 读取原始图像
        original = cv2.imread(sample_img_path)
        original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
        
        # 预处理图像
        gray = cv2.imread(sample_img_path, 0)
        resized = cv2.resize(gray, (50, 50))
        
        # 创建对比图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].imshow(original_rgb)
        axes[0].set_title('原始图像', fontsize=14, fontweight='bold')
        axes[0].axis('off')
        
        axes[1].imshow(gray, cmap='gray')
        axes[1].set_title('灰度化处理', fontsize=14, fontweight='bold')
        axes[1].axis('off')
        
        axes[2].imshow(resized, cmap='gray')
        axes[2].set_title('尺寸标准化 (50×50)', fontsize=14, fontweight='bold')
        axes[2].axis('off')
        
        plt.suptitle('图像预处理流程对比', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('图3_预处理对比.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("图像预处理对比已保存为 '图3_预处理对比.png'")
    else:
        print("未找到示例图片，请确保数据集已正确放置")

def generate_model_performance():
    """生成模型性能评估图表 - 论文图片位置6-9"""
    print("正在生成模型性能评估图表...")
    
    # 检查模型是否存在
    if not os.path.isfile(svm.location_data(1)):
        print("模型文件不存在，请先训练模型")
        return
    
    # 加载模型和数据
    svm.train_tests_plit()
    model = pickle.load(open('.\\File\\model.sav', 'rb'))
    prediction = model.predict(svm.X_test)
    
    # 1. 分类报告
    print("\n=== 分类报告 ===")
    report = classification_report(svm.y_test, prediction, target_names=svm.categories, output_dict=True)
    print(classification_report(svm.y_test, prediction, target_names=svm.categories))
    
    # 2. 混淆矩阵
    cm = confusion_matrix(svm.y_test, prediction)
    plt.figure(figsize=(16, 14))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=svm.categories, yticklabels=svm.categories,
                cbar_kws={'label': '样本数量'})
    plt.title('混淆矩阵热力图', fontsize=16, fontweight='bold')
    plt.xlabel('预测类别', fontsize=12)
    plt.ylabel('真实类别', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('图8_混淆矩阵.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. 各类别性能指标
    precision = precision_score(svm.y_test, prediction, average=None, zero_division=0)
    recall = recall_score(svm.y_test, prediction, average=None, zero_division=0)
    f1 = f1_score(svm.y_test, prediction, average=None, zero_division=0)
    
    plt.figure(figsize=(18, 10))
    x = np.arange(len(svm.categories))
    width = 0.25
    
    plt.bar(x - width, precision, width, label='精确率', alpha=0.8, color='skyblue')
    plt.bar(x, recall, width, label='召回率', alpha=0.8, color='lightgreen')
    plt.bar(x + width, f1, width, label='F1值', alpha=0.8, color='lightcoral')
    
    plt.xlabel('水果类别', fontsize=12)
    plt.ylabel('分数', fontsize=12)
    plt.title('各类别性能指标对比', fontsize=16, fontweight='bold')
    plt.xticks(x, svm.categories, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('图9_性能指标对比.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 4. 整体性能统计
    accuracy = accuracy_score(svm.y_test, prediction)
    avg_precision = np.mean(precision)
    avg_recall = np.mean(recall)
    avg_f1 = np.mean(f1)
    
    print(f"\n=== 整体性能统计 ===")
    print(f"准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"平均精确率: {avg_precision:.4f} ({avg_precision*100:.2f}%)")
    print(f"平均召回率: {avg_recall:.4f} ({avg_recall*100:.2f}%)")
    print(f"平均F1值: {avg_f1:.4f} ({avg_f1*100:.2f}%)")
    
    # 保存性能统计到文件
    with open('模型性能统计.txt', 'w', encoding='utf-8') as f:
        f.write("=== 模型性能统计报告 ===\n\n")
        f.write(f"准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n")
        f.write(f"平均精确率: {avg_precision:.4f} ({avg_precision*100:.2f}%)\n")
        f.write(f"平均召回率: {avg_recall:.4f} ({avg_recall*100:.2f}%)\n")
        f.write(f"平均F1值: {avg_f1:.4f} ({avg_f1*100:.2f}%)\n\n")
        f.write("=== 详细分类报告 ===\n")
        f.write(classification_report(svm.y_test, prediction, target_names=svm.categories))
    
    print("模型性能图表已生成完成")
    print("性能统计已保存为 '模型性能统计.txt'")

def main():
    """主函数：生成所有论文图表"""
    print("开始生成论文所需的图表...")
    print("=" * 50)
    
    # 创建输出目录
    if not os.path.exists('论文图表'):
        os.makedirs('论文图表')
    os.chdir('论文图表')
    
    try:
        # 生成各种图表
        generate_data_distribution()
        print("-" * 30)
        
        generate_sample_images()
        print("-" * 30)
        
        generate_preprocessing_comparison()
        print("-" * 30)
        
        generate_model_performance()
        print("-" * 30)
        
        print("\n所有图表生成完成！")
        print("请查看 '论文图表' 文件夹中的图片文件")
        print("\n图片文件清单：")
        print("- 图1_数据分布.png")
        print("- 图2_样本图像展示.png")
        print("- 图3_预处理对比.png")
        print("- 图8_混淆矩阵.png")
        print("- 图9_性能指标对比.png")
        print("- 模型性能统计.txt")
        
    except Exception as e:
        print(f"生成图表时出现错误: {str(e)}")
        print("请确保：")
        print("1. 数据集已正确放置在 FruitImages 文件夹中")
        print("2. 模型已经训练完成")
        print("3. 所有必要的库已安装")

if __name__ == "__main__":
    main()
