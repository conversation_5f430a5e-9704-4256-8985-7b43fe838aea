import os
import pickle
from tkinter import *
import numpy as np
from PIL import ImageTk, Image
from tkinter import filedialog, messagebox
import cv2
import Data_Classifer as svm

root = Tk()
root.geometry("500x500")
root.resizable(width=True, height=True)

# 打开文件对话框选择图像文件
def openfn():
    filename = filedialog.askopenfilename(title='选择图像文件')
    return filename

# 打开并显示图像
def open_img():
    x = openfn()
    img = Image.open(x)
    img = img.resize((200, 200), Image.ANTIALIAS)  # 调整图像大小用于显示
    img = ImageTk.PhotoImage(img)
    panel = Label(root, image=img)
    panel.image = img
    panel.place(x=120, y=150)  # 设置图像显示位置
    return x  # 返回图像文件路径

# 预处理图像数据
def prepare(filepath):
    img_array = cv2.imread(filepath, 0)  # 读取图像并转换为灰度图
    img_array = cv2.resize(img_array, (50, 50))  # 调整图像大小以匹配模型输入要求
    return np.array(img_array).flatten()  # 将图像展平为一维数组

# 评估和预测图像类别
def Evaluate():
    if (os.path.isfile(svm.location_data(1))):
        # 加载训练好的模型
        pick = open('.\\File\\model.sav', 'rb')
        model = pickle.load(pick)
        pick.close()

        # 对选择的图像进行预测
        prediction = model.predict([prepare(open_img())])

        # 显示预测结果
        error1 = Label(root, background='Orange', width=20, font=('Time New Roman', 10), text=svm.categories[prediction[0]])
        error1.place(x=170, y=430)
    else:
        messagebox.showerror('通知', '模型尚未创建!')

# 主标题标签
label = Label(root, width=32, font=('Time New Roman', 20), fg='red', text='水果分类识别', bg='yellow')
label.place(y=1)

# 说明标签
label1 = Label(root, width=40, font=('Time New Roman', 12), fg='black', text='请选择一张水果图片进行识别')
label1.place(x=70, y=60)

# 打开图像按钮
btn6 = Button(root, text='选择图像', width=10, font=('Time New Roman', 10), activebackground='Light Blue', command=Evaluate)
btn6.place(x=200, y=100)

root.mainloop()  # 启动GUI主循环