"""
修复版图表生成脚本
专门解决图表生成问题
"""

import os
import pickle
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, precision_score, recall_score, f1_score, accuracy_score
import Data_Classifer as svm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def ensure_model_exists():
    """确保模型存在，如果不存在则训练"""
    if not os.path.isfile(svm.location_data(1)):
        print("模型不存在，开始训练...")
        try:
            if not os.path.isfile(svm.location_data(0)):
                print("创建训练数据...")
                svm.create_training_data()
            
            print("分割数据...")
            svm.train_tests_plit()
            
            print("训练模型...")
            svm.SVM()
            print("模型训练完成！")
            return True
        except Exception as e:
            print(f"训练失败: {str(e)}")
            return False
    return True

def generate_data_distribution():
    """生成数据分布图"""
    print("生成数据分布图...")
    
    num_images = []
    valid_categories = []
    valid_display_names = []
    
    for i, category in enumerate(svm.categories):
        try:
            category_path = os.path.join(svm.dir, category)
            if os.path.exists(category_path):
                files = os.listdir(category_path)
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                count = len(image_files)
                if count > 0:
                    num_images.append(count)
                    valid_categories.append(category)
                    valid_display_names.append(svm.display_names[i])
        except Exception as e:
            print(f"处理类别 {category} 时出错: {str(e)}")
    
    if not num_images:
        print("没有找到有效的图像数据")
        return
    
    plt.figure(figsize=(15, 10))
    y_pos = np.arange(len(valid_categories))
    bars = plt.barh(y_pos, num_images, align='center', color='skyblue', alpha=0.8)
    plt.yticks(y_pos, valid_display_names)
    plt.xlabel('图像数量')
    plt.title('各类别水果图像数量分布')
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width + 1, bar.get_y() + bar.get_height()/2, 
                f'{num_images[i]}', ha='left', va='center')
    
    plt.tight_layout()
    plt.savefig('数据分布图.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 数据分布图已保存")

def generate_sample_images():
    """生成样本图像展示"""
    print("生成样本图像展示...")
    
    # 选择前12个有图像的类别进行展示
    valid_samples = []
    for i, category in enumerate(svm.categories):
        if len(valid_samples) >= 12:
            break
        try:
            category_path = os.path.join(svm.dir, category)
            if os.path.exists(category_path):
                files = os.listdir(category_path)
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                if image_files:
                    img_path = os.path.join(category_path, image_files[0])
                    img = cv2.imread(img_path)
                    if img is not None:
                        valid_samples.append((img, svm.display_names[i]))
        except Exception as e:
            print(f"处理类别 {category} 时出错: {str(e)}")
    
    if not valid_samples:
        print("没有找到有效的样本图像")
        return
    
    # 创建网格显示
    rows = 3
    cols = 4
    fig, axes = plt.subplots(rows, cols, figsize=(16, 12))
    fig.suptitle('水果样本图像展示', fontsize=16, fontweight='bold')
    
    for i in range(rows * cols):
        row = i // cols
        col = i % cols
        
        if i < len(valid_samples):
            img, name = valid_samples[i]
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            axes[row, col].imshow(img_rgb)
            axes[row, col].set_title(name, fontsize=12, fontweight='bold')
        else:
            axes[row, col].text(0.5, 0.5, '无图像', ha='center', va='center', 
                              transform=axes[row, col].transAxes)
        axes[row, col].axis('off')
    
    plt.tight_layout()
    plt.savefig('样本图像展示.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 样本图像展示已保存")

def generate_preprocessing_demo():
    """生成预处理演示"""
    print("生成预处理演示...")
    
    # 找到第一张可用图片
    sample_img_path = None
    for category in svm.categories[:5]:
        category_path = os.path.join(svm.dir, category)
        if os.path.exists(category_path):
            files = os.listdir(category_path)
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            if image_files:
                sample_img_path = os.path.join(category_path, image_files[0])
                break
    
    if not sample_img_path or not os.path.exists(sample_img_path):
        print("未找到示例图片")
        return
    
    # 读取和处理图像
    original = cv2.imread(sample_img_path)
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    gray = cv2.imread(sample_img_path, 0)
    resized = cv2.resize(gray, (50, 50))
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(original_rgb)
    axes[0].set_title('原始图像', fontsize=14, fontweight='bold')
    axes[0].axis('off')
    
    axes[1].imshow(gray, cmap='gray')
    axes[1].set_title('灰度化处理', fontsize=14, fontweight='bold')
    axes[1].axis('off')
    
    axes[2].imshow(resized, cmap='gray')
    axes[2].set_title('尺寸标准化 (50×50)', fontsize=14, fontweight='bold')
    axes[2].axis('off')
    
    plt.suptitle('图像预处理流程', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('预处理演示.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✅ 预处理演示已保存")

def generate_model_performance():
    """生成模型性能图表"""
    print("生成模型性能图表...")
    
    if not ensure_model_exists():
        return
    
    try:
        # 重新分割数据并预测
        svm.train_tests_plit()
        model = pickle.load(open(svm.location_data(1), 'rb'))
        prediction = model.predict(svm.X_test)
        
        # 计算性能指标
        accuracy = accuracy_score(svm.y_test, prediction)
        
        # 混淆矩阵
        cm = confusion_matrix(svm.y_test, prediction)
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('混淆矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        plt.savefig('混淆矩阵.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 性能统计
        print(f"\n模型性能统计:")
        print(f"准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 保存性能报告
        with open('性能报告.txt', 'w', encoding='utf-8') as f:
            f.write(f"模型性能报告\n")
            f.write(f"=" * 30 + "\n")
            f.write(f"准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n")
            f.write(f"测试样本数: {len(svm.y_test)}\n")
            f.write(f"类别数: {len(svm.categories)}\n")
        
        print("✅ 模型性能图表已保存")
        
    except Exception as e:
        print(f"生成性能图表时出错: {str(e)}")

def main():
    """主函数"""
    print("开始生成修复版图表...")
    print("=" * 50)
    
    try:
        generate_data_distribution()
        print("-" * 30)
        
        generate_sample_images()
        print("-" * 30)
        
        generate_preprocessing_demo()
        print("-" * 30)
        
        generate_model_performance()
        print("-" * 30)
        
        print("\n🎉 所有图表生成完成！")
        print("\n生成的文件:")
        print("- 数据分布图.png")
        print("- 样本图像展示.png")
        print("- 预处理演示.png")
        print("- 混淆矩阵.png")
        print("- 性能报告.txt")
        
    except Exception as e:
        print(f"生成图表时出现错误: {str(e)}")

if __name__ == "__main__":
    main()
