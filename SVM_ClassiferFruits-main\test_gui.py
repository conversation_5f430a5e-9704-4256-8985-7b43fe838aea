"""
简化的GUI测试
测试GUI功能是否正常
"""

import os
import sys
from tkinter import *
from tkinter import messagebox
import Data_Classifer as svm

def test_create_data():
    """测试创建数据功能"""
    try:
        if not os.path.isfile(svm.location_data(0)):
            svm.create_training_data()
            messagebox.showinfo('通知', '训练数据已创建')
        else:
            messagebox.showwarning('通知', '文件已存在!')
    except Exception as e:
        messagebox.showerror('错误', f'创建数据失败: {str(e)}')

def test_split_data():
    """测试数据分割功能"""
    try:
        if os.path.isfile(svm.location_data(0)):
            svm.train_tests_plit()
            messagebox.showinfo('通知', '训练集和测试集已创建')
        else:
            messagebox.showwarning('通知', '还没有图像数据！请先导入数据')
    except Exception as e:
        messagebox.showerror('错误', f'数据分割失败: {str(e)}')

def test_train_model():
    """测试模型训练功能"""
    try:
        if not os.path.isfile(svm.location_data(1)):
            svm.SVM()
            messagebox.showinfo('通知', '模型训练已完成!')
        else:
            messagebox.showwarning('通知', '训练模型已存在!')
    except Exception as e:
        messagebox.showerror('错误', f'模型训练失败: {str(e)}')

def test_prediction():
    """测试预测功能"""
    try:
        if os.path.isfile(svm.location_data(1)):
            accuracy = svm.Result(2)
            prediction = svm.Result(1)
            
            # 显示结果
            result_text = f"准确率: {accuracy}\n预测示例: {prediction}"
            messagebox.showinfo('预测结果', result_text)
        else:
            messagebox.showerror('错误', '模型文件不存在')
    except Exception as e:
        messagebox.showerror('错误', f'预测失败: {str(e)}')

def test_visualization():
    """测试可视化功能"""
    try:
        svm.visualize()
        messagebox.showinfo('通知', '数据可视化完成')
    except Exception as e:
        messagebox.showerror('错误', f'可视化失败: {str(e)}')

def delete_files():
    """删除文件功能"""
    try:
        if os.listdir(svm.folder):
            svm.Delete_Data()
            messagebox.showinfo('通知', '模型已删除，现在可以训练新模型!')
        else:
            messagebox.showwarning('通知', '文件夹为空!')
    except Exception as e:
        messagebox.showerror('错误', f'删除失败: {str(e)}')

def main():
    """主GUI函数"""
    try:
        # 创建主窗口
        root = Tk()
        root.title("水果分类系统测试")
        root.geometry('700x400')
        
        # 主标题
        title_label = Label(root, text="水果分类系统功能测试", 
                           font=('Arial', 16, 'bold'), fg='red', bg='yellow')
        title_label.pack(pady=10)
        
        # 说明文字
        info_label = Label(root, text="点击下面的按钮测试各项功能", 
                          font=('Arial', 12))
        info_label.pack(pady=5)
        
        # 按钮框架
        button_frame = Frame(root)
        button_frame.pack(pady=20)
        
        # 创建按钮
        buttons = [
            ("1. 导入数据", test_create_data),
            ("2. 创建训练测试集", test_split_data),
            ("3. 训练模型", test_train_model),
            ("4. 测试预测", test_prediction),
            ("5. 数据可视化", test_visualization),
            ("6. 删除文件", delete_files)
        ]
        
        for i, (text, command) in enumerate(buttons):
            row = i // 2
            col = i % 2
            
            btn = Button(button_frame, text=text, command=command,
                        width=20, height=2, font=('Arial', 10))
            btn.grid(row=row, column=col, padx=10, pady=5)
        
        # 状态信息
        status_frame = Frame(root)
        status_frame.pack(pady=20)
        
        status_text = f"""
当前状态:
- 数据文件存在: {'是' if os.path.exists(svm.location_data(0)) else '否'}
- 模型文件存在: {'是' if os.path.exists(svm.location_data(1)) else '否'}
- FruitImages文件夹存在: {'是' if os.path.exists(svm.dir) else '否'}
        """
        
        status_label = Label(status_frame, text=status_text, 
                           font=('Arial', 10), justify=LEFT)
        status_label.pack()
        
        # 退出按钮
        quit_btn = Button(root, text="退出", command=root.quit,
                         width=10, height=1, font=('Arial', 10), bg='lightcoral')
        quit_btn.pack(pady=10)
        
        print("GUI启动成功！")
        print("如果您看到这条消息但没有看到GUI窗口，可能是显示问题。")
        
        # 启动GUI
        root.mainloop()
        
    except Exception as e:
        print(f"GUI启动失败: {str(e)}")
        print("这可能是因为:")
        print("1. 没有图形界面环境")
        print("2. tkinter库有问题")
        print("3. 显示设置问题")

if __name__ == "__main__":
    main()
