# 水果分类系统运行指南

## 环境准备

### 1. 安装必要的Python库

```bash
pip install opencv-python
pip install scikit-learn
pip install matplotlib
pip install numpy
pip install pillow
pip install tkinter
```

### 2. 数据集准备

1. 从 https://www.vicos.si/resources/fids30/ 下载FIDS30数据集
2. 解压数据集到项目目录下的 `FruitImages` 文件夹
3. 确保文件夹结构如下：
```
SVM_ClassiferFruits-main/
├── FruitImages/
│   ├── Acerola/
│   ├── Apple/
│   ├── Apricot/
│   ├── ...（30个水果类别文件夹）
├── File/（用于存储模型和数据文件，程序会自动创建）
├── Data_Classifer.py
├── GUI.py
├── ClassiferFruit.py
└── README.md
```

## 运行步骤

### 方法一：使用GUI界面（推荐）

1. **启动主界面**
```bash
python GUI.py
```

2. **按顺序执行以下步骤**：
   - 点击"导入数据"按钮：创建训练数据文件
   - 点击"创建训练测试集"按钮：分割数据为训练集和测试集
   - 点击"训练模型"按钮：训练SVM模型
   - 点击"预测"按钮：查看模型性能和预测结果
   - 点击"数据可视化"按钮：查看数据分布图表

3. **使用图像识别功能**
```bash
python ClassiferFruit.py
```
   - 点击"选择图像"按钮上传水果图片
   - 系统会自动显示预测结果

### 方法二：直接调用函数

```python
import Data_Classifer as svm

# 1. 创建训练数据
svm.create_training_data()

# 2. 分割训练测试集
svm.train_tests_plit()

# 3. 训练SVM模型
svm.SVM()

# 4. 查看结果
print("模型准确率:", svm.Result(2))
print("预测结果:", svm.Result(1))

# 5. 显示测试图像
svm.Display()

# 6. 可视化数据分布
svm.visualize()
```

## 获取论文所需的图片和数据

### 1. 数据分布可视化
运行以下代码获取各类别图像数量分布图：
```python
import Data_Classifer as svm
svm.visualize()
```
**保存图片用于论文图片插入位置1**

### 2. 原始图像样本展示
手动从FruitImages文件夹中选择代表性图像，制作样本展示图
**用于论文图片插入位置2**

### 3. 图像预处理对比
```python
import cv2
import matplotlib.pyplot as plt

# 读取原始图像
original = cv2.imread('path/to/fruit/image.jpg')
# 预处理后的图像
processed = cv2.imread('path/to/fruit/image.jpg', 0)
processed = cv2.resize(processed, (50, 50))

# 显示对比
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))
ax1.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
ax1.set_title('原始图像')
ax2.imshow(processed, cmap='gray')
ax2.set_title('预处理后图像')
plt.show()
```
**保存图片用于论文图片插入位置3**

### 4. 特征提取流程图
手动绘制或使用绘图工具创建流程图，显示：
原始图像 → 灰度化 → 尺寸调整 → 特征向量
**用于论文图片插入位置4**

### 5. SVM算法原理图
使用绘图工具创建SVM分类原理示意图
**用于论文图片插入位置5**

### 6. 模型性能评估
```python
import Data_Classifer as svm
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 训练模型并获取预测结果
svm.train_tests_plit()
model = pickle.load(open('.\\File\\model.sav', 'rb'))
prediction = model.predict(svm.X_test)

# 1. 分类报告
print(classification_report(svm.y_test, prediction, target_names=svm.categories))

# 2. 混淆矩阵
cm = confusion_matrix(svm.y_test, prediction)
plt.figure(figsize=(15, 12))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=svm.categories, yticklabels=svm.categories)
plt.title('混淆矩阵')
plt.xlabel('预测类别')
plt.ylabel('真实类别')
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()

# 3. 各类别准确率
from sklearn.metrics import precision_score, recall_score, f1_score
precision = precision_score(svm.y_test, prediction, average=None)
recall = recall_score(svm.y_test, prediction, average=None)
f1 = f1_score(svm.y_test, prediction, average=None)

plt.figure(figsize=(15, 8))
x = range(len(svm.categories))
plt.bar(x, precision, alpha=0.7, label='精确率')
plt.bar(x, recall, alpha=0.7, label='召回率')
plt.bar(x, f1, alpha=0.7, label='F1值')
plt.xlabel('水果类别')
plt.ylabel('分数')
plt.title('各类别性能指标对比')
plt.xticks(x, svm.categories, rotation=45)
plt.legend()
plt.tight_layout()
plt.show()
```
**保存图片用于论文图片插入位置6-9**

## 常见问题解决

### 1. 导入错误
如果遇到`ModuleNotFoundError`，请确保已安装所有必要的库：
```bash
pip install -r requirements.txt
```

### 2. 数据集路径问题
确保FruitImages文件夹在正确位置，并包含30个子文件夹

### 3. 内存不足
如果数据集过大导致内存不足，可以：
- 减少图像尺寸（修改resize参数）
- 减少训练样本数量
- 分批处理数据

### 4. 模型训练时间过长
- 减少训练样本数量
- 调整SVM参数
- 使用更简单的核函数

## 实验数据记录

运行完整实验后，请记录以下数据用于论文：

1. **数据集统计**：
   - 总图像数量：_____
   - 各类别图像数量：_____
   - 训练集大小：_____
   - 测试集大小：_____

2. **模型性能**：
   - 整体准确率：_____%
   - 平均精确率：_____%
   - 平均召回率：_____%
   - 平均F1值：_____%

3. **训练时间**：
   - 数据预处理时间：_____秒
   - 模型训练时间：_____秒
   - 预测时间：_____秒

4. **最佳/最差分类类别**：
   - 准确率最高的3个类别：_____
   - 准确率最低的3个类别：_____

## 论文图片清单

请按照以下顺序准备论文插图：

1. **图片插入位置1**：数据集各类别样本数量分布条形图
2. **图片插入位置2**：30种水果的代表性原始图像样本展示
3. **图片插入位置3**：图像预处理前后对比图
4. **图片插入位置4**：特征提取流程示意图
5. **图片插入位置5**：SVM算法原理和核函数映射示意图
6. **图片插入位置6**：模型训练过程可视化图表
7. **图片插入位置7**：分类报告截图（包含精确率、召回率、F1值）
8. **图片插入位置8**：30×30混淆矩阵热力图
9. **图片插入位置9**：各类别分类准确率对比条形图

完成实验后，将这些图片按顺序插入到论文的相应位置即可。
