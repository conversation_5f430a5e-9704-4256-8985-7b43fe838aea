"""
路径诊断脚本
检查所有路径是否正确
"""

import os
import sys

def check_paths():
    print("=== 路径诊断报告 ===")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    print()
    
    # 检查相对路径
    relative_fruit_path = '.\\FruitImages'
    absolute_fruit_path = os.path.abspath(relative_fruit_path)
    
    print(f"相对路径: {relative_fruit_path}")
    print(f"绝对路径: {absolute_fruit_path}")
    print(f"FruitImages文件夹存在: {os.path.exists(relative_fruit_path)}")
    print()
    
    # 检查脚本同目录下的路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    fruit_path_in_script_dir = os.path.join(script_dir, 'FruitImages')
    
    print(f"脚本目录下的FruitImages路径: {fruit_path_in_script_dir}")
    print(f"脚本目录下FruitImages存在: {os.path.exists(fruit_path_in_script_dir)}")
    print()
    
    # 列出当前目录内容
    print("当前目录内容:")
    try:
        for item in os.listdir('.'):
            item_path = os.path.join('.', item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
            else:
                print(f"  📄 {item}")
    except Exception as e:
        print(f"  错误: {e}")
    print()
    
    # 检查FruitImages内容
    if os.path.exists(relative_fruit_path):
        print("FruitImages文件夹内容:")
        try:
            items = os.listdir(relative_fruit_path)
            for item in items[:10]:  # 只显示前10个
                item_path = os.path.join(relative_fruit_path, item)
                if os.path.isdir(item_path):
                    # 计算该类别的图片数量
                    try:
                        files = os.listdir(item_path)
                        image_count = len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])
                        print(f"  📁 {item}/ ({image_count} 张图片)")
                    except:
                        print(f"  📁 {item}/ (无法读取)")
                else:
                    print(f"  📄 {item}")
            if len(items) > 10:
                print(f"  ... 还有 {len(items) - 10} 个项目")
        except Exception as e:
            print(f"  错误: {e}")
    else:
        print("❌ FruitImages文件夹不存在!")
    print()
    
    # 检查File文件夹
    file_folder = '.\\File'
    print(f"File文件夹路径: {file_folder}")
    print(f"File文件夹存在: {os.path.exists(file_folder)}")
    
    if os.path.exists(file_folder):
        print("File文件夹内容:")
        try:
            for item in os.listdir(file_folder):
                print(f"  📄 {item}")
        except Exception as e:
            print(f"  错误: {e}")
    print()
    
    # 推荐解决方案
    print("=== 推荐解决方案 ===")
    if not os.path.exists(relative_fruit_path):
        print("1. 确保您在正确的目录下运行脚本")
        print("   应该在 SVM_ClassiferFruits-main 文件夹内运行")
        print()
        print("2. 或者修改代码使用绝对路径")
        print(f"   将 dir = '.\\FruitImages' 改为:")
        print(f"   dir = r'{fruit_path_in_script_dir}'")
        print()
        print("3. 检查FruitImages文件夹是否在正确位置")
        print("   应该与Python脚本在同一目录下")

if __name__ == "__main__":
    check_paths()
