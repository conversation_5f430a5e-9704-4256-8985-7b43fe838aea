"""
调试路径问题的脚本
"""

import os
import Data_Classifer as svm

def debug_paths():
    print("=== 路径调试信息 ===")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {svm.script_dir}")
    print(f"数据集路径: {svm.dir}")
    print(f"File文件夹路径: {svm.folder}")
    
    print("\n=== 检查路径是否存在 ===")
    print(f"数据集文件夹存在: {os.path.exists(svm.dir)}")
    print(f"File文件夹存在: {os.path.exists(svm.folder)}")
    
    if os.path.exists(svm.dir):
        print(f"\n=== 数据集文件夹内容 ===")
        try:
            folders = [f for f in os.listdir(svm.dir) if os.path.isdir(os.path.join(svm.dir, f))]
            print(f"找到 {len(folders)} 个子文件夹:")
            for folder in folders[:10]:  # 只显示前10个
                print(f"  - {folder}")
            if len(folders) > 10:
                print(f"  ... 还有 {len(folders) - 10} 个文件夹")
        except Exception as e:
            print(f"读取数据集文件夹时出错: {str(e)}")
    
    if os.path.exists(svm.folder):
        print(f"\n=== File文件夹内容 ===")
        try:
            files = os.listdir(svm.folder)
            print(f"找到 {len(files)} 个文件:")
            for file in files:
                print(f"  - {file}")
        except Exception as e:
            print(f"读取File文件夹时出错: {str(e)}")
    
    print("\n=== 检查类别文件夹 ===")
    missing_categories = []
    existing_categories = []
    
    for category in svm.categories[:5]:  # 只检查前5个类别
        category_path = os.path.join(svm.dir, category)
        if os.path.exists(category_path):
            try:
                files = os.listdir(category_path)
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                existing_categories.append((category, len(image_files)))
                print(f"✅ {category}: {len(image_files)} 张图片")
            except Exception as e:
                print(f"❌ {category}: 读取失败 - {str(e)}")
                missing_categories.append(category)
        else:
            print(f"❌ {category}: 文件夹不存在")
            missing_categories.append(category)
    
    print(f"\n=== 总结 ===")
    print(f"存在的类别: {len(existing_categories)}")
    print(f"缺失的类别: {len(missing_categories)}")
    
    if missing_categories:
        print("缺失的类别:", missing_categories)
    
    return len(missing_categories) == 0

if __name__ == "__main__":
    success = debug_paths()
    if success:
        print("\n🎉 路径检查通过！")
    else:
        print("\n❌ 路径检查失败，请检查数据集位置")
