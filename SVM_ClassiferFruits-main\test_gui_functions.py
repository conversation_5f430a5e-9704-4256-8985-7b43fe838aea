"""
测试GUI功能的脚本
模拟GUI中的各个操作
"""

import os
import Data_Classifer as svm

def test_all_functions():
    print("=== 测试GUI功能 ===")
    
    # 1. 测试数据导入
    print("\n1. 测试数据导入...")
    try:
        if not os.path.isfile(svm.location_data(0)):
            print("   创建训练数据...")
            svm.create_training_data()
            print("   ✅ 训练数据创建成功")
        else:
            print("   ✅ 训练数据已存在")
    except Exception as e:
        print(f"   ❌ 训练数据创建失败: {str(e)}")
        return False
    
    # 2. 测试数据分割
    print("\n2. 测试数据分割...")
    try:
        svm.train_tests_plit()
        print("   ✅ 数据分割成功")
    except Exception as e:
        print(f"   ❌ 数据分割失败: {str(e)}")
        return False
    
    # 3. 测试模型训练
    print("\n3. 测试模型训练...")
    try:
        if not os.path.isfile(svm.location_data(1)):
            print("   训练SVM模型...")
            svm.SVM()
            print("   ✅ 模型训练成功")
        else:
            print("   ✅ 模型已存在")
    except Exception as e:
        print(f"   ❌ 模型训练失败: {str(e)}")
        return False
    
    # 4. 测试预测
    print("\n4. 测试预测...")
    try:
        accuracy = svm.Result(2)
        prediction = svm.Result(1)
        print(f"   ✅ 预测成功")
        print(f"   准确率: {accuracy}")
        print(f"   预测示例: {prediction}")
    except Exception as e:
        print(f"   ❌ 预测失败: {str(e)}")
        return False
    
    # 5. 测试可视化
    print("\n5. 测试可视化...")
    try:
        # 不实际显示图表，只测试数据处理
        num_images = []
        for lab in svm.categories[:5]:  # 只测试前5个类别
            try:
                files = os.listdir(os.path.join(svm.dir, lab))
                image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
                c = len(image_files)
                num_images.append(c)
            except FileNotFoundError:
                num_images.append(0)
        
        if sum(num_images) > 0:
            print("   ✅ 可视化数据准备成功")
        else:
            print("   ❌ 可视化数据准备失败")
            return False
    except Exception as e:
        print(f"   ❌ 可视化测试失败: {str(e)}")
        return False
    
    print("\n🎉 所有GUI功能测试通过！")
    return True

def test_file_paths():
    """测试文件路径"""
    print("\n=== 测试文件路径 ===")
    
    paths_to_check = [
        ("数据集文件夹", svm.dir),
        ("File文件夹", svm.folder),
        ("数据文件", svm.location_data(0)),
        ("模型文件", svm.location_data(1))
    ]
    
    all_good = True
    for name, path in paths_to_check:
        if os.path.exists(path):
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path} (不存在)")
            if name in ["数据集文件夹", "File文件夹"]:
                all_good = False
    
    return all_good

if __name__ == "__main__":
    print("开始测试...")
    
    # 测试路径
    if not test_file_paths():
        print("❌ 路径测试失败")
        exit(1)
    
    # 测试功能
    if test_all_functions():
        print("\n✅ 所有测试通过！GUI应该可以正常工作了。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
